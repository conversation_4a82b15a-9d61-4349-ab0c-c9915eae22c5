package server

import (
	"fmt"
	"transaction-service/core/config"
	"transaction-service/core/connect"
	"transaction-service/core/database"
	"transaction-service/core/rabbitmq"
	"transaction-service/core/redis"
	"transaction-service/core/templates"
	"transaction-service/core/worker"
	"transaction-service/internal/repositories"
	"transaction-service/internal/services"
	"transaction-service/pkg/logger"
	"transaction-service/tests/mocks"

	"github.com/gin-gonic/gin"
	// "gorm.io/gorm"
)

// InitTest initializes the server for testing environment
func (s *Server) InitTest() (*gin.Engine, *worker.RabbitMQHandler, error) {
	// Load test configuration
	conf, err := config.LoadTest()
	if err != nil {
		fmt.Printf("err config.LoadTest() %s\n", err)
		return nil, nil, err
	}

	logger.Init("test.log")
	fmt.Println("Initializing test environment...")

	//postgres
	dbase, err := database.Connect(conf.Database)
	if err != nil {
		fmt.Printf("err db.Connect() %s\n", err)
		return nil, nil, err
	}

	// Reset database for testing
	// if err := s.resetTestDatabase(dbase); err != nil {
	// 	fmt.Printf("err resetTestDatabase() %s\n", err)
	// 	return nil, nil, err
	// }

	//init fileManager
	fileManager := templates.NewFileManager(conf.TemplatePath)

	//init redis
	rds := redis.New(conf.Redis)
	//init entities
	repo := repositories.NewRepository(dbase, rds)
	service := services.NewService(repo, *conf, *fileManager)

	// Setup mock HTTP client for testing
	restMock := mocks.NewRestClientMock()
	restMock.Setup()
	restMock.SetupDefaultMocks()

	// Store mock client for test access
	connect.TestRestMock = restMock

	//init rabbitmq
	rab := rabbitmq.NewRabbitMq()
	err = rab.Init(conf.RabbitMQ)
	if err != nil {
		fmt.Println(err.Error())
		fmt.Printf("err rabbitmq connect %s\n", err)
	}

	//init real logger
	loggerr, err := logger.NewLogger(config.Get().Dir.LogPath)
	if err != nil {
		fmt.Printf("error init log zap")
	}
	defer loggerr.Sync()

	rabHandler := worker.NewRabbitMQHandler(service, rab, loggerr)

	connect.DB = dbase
	connect.Redis = rds
	connect.RabbitMQ = rab
	connect.Logger = loggerr

	//routing api with test middleware
	r := SetupRoutesWithDeps(service)

	connect.Router = r

	return r, rabHandler, nil
}

// resetTestDatabase drops and recreates all tables
// func (s *Server) resetTestDatabase(db *gorm.DB) error {
// 	fmt.Println("Resetting test database...")

// 	// Auto-migrate all models
// 	err := database.AutoMigrate(db)
// 	if err != nil {
// 		return fmt.Errorf("failed to auto-migrate: %w", err)
// 	}

// 	fmt.Println("Test database reset complete")
// 	return nil
// }
