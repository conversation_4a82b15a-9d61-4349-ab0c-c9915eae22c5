# HTTP Client Architecture Refactoring - Complete

## Overview

Successfully completed the architectural refactoring of HTTP client dependency injection and mock integration. The new architecture provides centralized HTTP client management with selective mocking capabilities while maintaining clean separation of concerns.

## New Architecture

### 1. Centralized HTTP Client Management

```
core/
├── connect/
│   └── connect.go          # Global HTTP client storage
├── http/
│   └── client.go           # Selective HTTP client implementation
└── server/
    ├── server.go           # Production initialization
    └── tests.go            # Test initialization
```

**Key Components:**

1. **`core/connect/connect.go`** - Centralized storage
   ```go
   type HTTPClientInterface interface {
       Do(req *http.Request) (*http.Response, error)
   }
   
   var HTTPClient HTTPClientInterface // Singleton HTTP client
   ```

2. **`core/http/client.go`** - Selective mocking implementation
   ```go
   type SelectiveHTTPClient struct {
       realClient connect.HTTPClientInterface
       mockClient connect.HTTPClientInterface
       useMock    bool
   }
   ```

3. **Server Initialization** - Automatic setup
   ```go
   // Production: core/server/server.go
   connect.HTTPClient = httpClient.NewSelectiveHTTPClient(&http.Client{})
   
   // Test: core/server/tests.go  
   connect.HTTPClient = httpClient.NewSelectiveHTTPClient(&http.Client{})
   restMock.Setup() // Configures mocking
   ```

### 2. Service Integration

**Before (Package-Level Dependency):**
```go
// internal/integration/rest/auth.go
var httpClient HTTPClient = &http.Client{}

func SetHTTPClient(client HTTPClient) {
    httpClient = client
}

func FetchUserRoles() ([]AdminsDto, error) {
    resp, err := httpClient.Do(req)
    // ...
}
```

**After (Centralized Access):**
```go
// internal/integration/rest/auth.go
import "transaction-service/core/connect"

func FetchUserRoles() ([]AdminsDto, error) {
    resp, err := connect.HTTPClient.Do(req)
    // ...
}
```

### 3. Selective Mocking System

**Capabilities:**
- **Selective Mocking**: Mock specific requests while allowing others to use real client
- **Runtime Control**: Enable/disable mocking during test execution
- **Fallback Behavior**: Graceful fallback to real client when no mock configured
- **Request Verification**: Capture and verify all HTTP requests made

**Usage Examples:**

```go
// Enable mocking for specific test
httpClient.SetMockClient(mockClient)

// Disable mocking to use real client
httpClient.DisableMock()

// Check mocking status
if httpClient.IsMockEnabled() {
    // Mocking is active
}

// Re-enable mocking
httpClient.EnableMock()
```

## Benefits Achieved

### ✅ **Centralized Management**
- **Single Source of Truth**: HTTP client stored in `core/connect/connect.go`
- **Consistent Access**: All services use `connect.HTTPClient`
- **Simplified Configuration**: Initialized once during server startup

### ✅ **Selective Mocking**
- **Flexible Testing**: Mock specific requests while allowing others to use real client
- **Runtime Control**: Enable/disable mocking during test execution
- **Error Simulation**: Support for all types of HTTP errors and network failures

### ✅ **Clean Architecture**
- **Separation of Concerns**: HTTP client management separated from business logic
- **Maintainable Code**: Centralized configuration and initialization
- **Extensible Design**: Easy to add new HTTP client features

### ✅ **Backward Compatibility**
- **Unchanged APIs**: `FetchUserRoles()` function signature preserved
- **Existing Tests**: All existing functionality maintained
- **Gradual Migration**: Can be applied to other services incrementally

### ✅ **Improved Testing**
- **Better Organization**: Mock code organized in dedicated packages
- **Comprehensive Coverage**: Support for all error scenarios
- **Easy Verification**: Request capture and verification capabilities

## Usage Examples

### Basic Test Setup
```go
func TestSomething(t *testing.T) {
    // Initialize test server (automatic mock setup)
    s := &server.Server{Test: true}
    _, _, err := s.InitTest()
    require.NoError(t, err)

    // Get mock client
    restMock, ok := connect.TestRestMock.(*mocks.RestClientMock)
    require.True(t, ok)

    // Configure mock response
    restMock.Reset()
    restMock.MockFetchUserRolesSuccess(testAdmins)

    // Test your code
    result, err := someFunction()
    
    // Verify results and calls
    assert.NoError(t, err)
    assert.True(t, restMock.VerifyFetchUserRolesCalled())
}
```

### Selective Mocking
```go
func TestSelectiveMocking(t *testing.T) {
    // Mock specific requests
    restMock.MockFetchUserRolesSuccess(testAdmins)
    
    // This call uses mock
    admins, err := rest.FetchUserRoles()
    assert.NoError(t, err)
    
    // Disable mocking for integration test
    httpClient.DisableMock()
    
    // This call uses real client (may fail in test environment)
    _, err = rest.FetchUserRoles()
    // Handle real response or expected failure
    
    // Re-enable mocking
    httpClient.EnableMock()
}
```

### Error Scenario Testing
```go
func TestErrorHandling(t *testing.T) {
    restMock.Reset()
    
    // Test different error scenarios
    restMock.MockFetchUserRolesUnauthorized()
    _, err := rest.FetchUserRoles()
    assert.Contains(t, err.Error(), "401")
    
    restMock.MockFetchUserRolesTimeout()
    _, err = rest.FetchUserRoles()
    assert.Contains(t, err.Error(), "timeout")
    
    restMock.MockFetchUserRolesInternalError()
    _, err = rest.FetchUserRoles()
    assert.Contains(t, err.Error(), "500")
}
```

## Files Modified/Created

### Modified Files:
- `core/connect/connect.go` - Added centralized HTTP client storage
- `core/server/server.go` - Added HTTP client initialization
- `core/server/tests.go` - Added test HTTP client setup
- `internal/integration/rest/auth.go` - Updated to use centralized client
- `tests/mocks/rest_client.go` - Updated to use centralized mocking
- `tests/unit/rest_auth_test.go` - Updated test patterns

### Created Files:
- `core/http/client.go` - Selective HTTP client implementation
- `tests/examples/selective_mocking_demo_test.go` - Demonstration examples

## Next Steps

The refactoring is complete and provides:

1. **Immediate Benefits**: Centralized HTTP client management with selective mocking
2. **Future Extensibility**: Easy to add features like retry logic, circuit breakers, etc.
3. **Scalability**: Pattern can be applied to other external service integrations
4. **Maintainability**: Clean, organized code structure

## Migration Guide for Other Services

To apply this pattern to other external service calls:

1. **Update Service Code**: Replace direct HTTP client usage with `connect.HTTPClient`
2. **Add Mock Methods**: Extend `RestClientMock` with service-specific methods
3. **Update Tests**: Use the new centralized mocking approach
4. **Verify Integration**: Ensure proper initialization in server startup

The architecture is now ready for production use and provides a solid foundation for future HTTP client enhancements.
