package rest

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"transaction-service/core/config"
)

// HTTPClient interface for making HTTP requests
type HTTPClient interface {
	Do(req *http.Request) (*http.Response, error)
}

// Package-level HTTP client variable for dependency injection
var httpClient HTTPClient = &http.Client{}

// SetHTTPClient sets the HTTP client for the package (used for testing)
func SetHTTPClient(client HTTPClient) {
	httpClient = client
}

// GetHTTPClient returns the current HTTP client
func GetHTTPClient() HTTPClient {
	return httpClient
}

func FetchUserRoles() ([]AdminsDto, error) {
	// Create HTTP request
	req, err := http.NewRequest("GET", config.Get().AuthService.Host+"api/v1/user/get-all-admins", nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add Authorization header
	req.Header.Set("Authorization", "Bearer "+config.Get().AuthService.Token)

	// Execute the request using the injected client
	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read and decode the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
	}

	var admins []AdminsDto
	if err = json.Unmarshal(body, &admins); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}

	return admins, nil
}
