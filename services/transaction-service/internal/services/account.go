package services

import (
	"fmt"
	"transaction-service/internal/dto"
	"transaction-service/internal/models"
	"transaction-service/internal/repositories"

	"github.com/shopspring/decimal"
)

type AccountServiceInterface interface {
	Create(dto *dto.AccountCreateDto) (*models.Account, error)
	CreateMinimal(dto *dto.MinimalAccountCreateDto) (*models.Account, error)
	GetOwnAccounts(ownerId uint, query *dto.AccountOwnQuery) ([]models.Account, error)
	GetAllAccounts(query *dto.AccountQuery, pag *dto.PaginationDto) (*dto.GetAllAccountDto, error)
	GetAccountById(id uint) (*models.Account, error)
	UpdateAccount(account *models.Account) error
	UpdateAmount(id uint, newAmount decimal.Decimal) error
	GetOwnBalances(userId uint, currencyId uint) (decimal.Decimal, error)
	DeleteAccountById(Id uint) error
	CheckCurrencyLimit(currencyId uint) (bool, error)
}

type AccountService struct {
	repo         repositories.AccountRepositoryInterface
	bankRepo     repositories.BankRepositoryInterface
	feeRepo      repositories.FeeRepositoryInterface
	currencyRepo repositories.CurrencyRepositoryInterface
}

func NewAccountService(
	repo repositories.AccountRepositoryInterface,
	bankRepo repositories.BankRepositoryInterface,
	feeRepo repositories.FeeRepositoryInterface,
	currencyRepo repositories.CurrencyRepositoryInterface) *AccountService {
	return &AccountService{
		repo:         repo,
		bankRepo:     bankRepo,
		feeRepo:      feeRepo,
		currencyRepo: currencyRepo,
	}
}

func (as *AccountService) Create(dto *dto.AccountCreateDto) (*models.Account, error) {
	var accountModel = models.Account{
		Name:              dto.Name,
		OwnerId:           dto.OwnerId,
		AssetType:         dto.AssetType,
		Status:            dto.Status,
		BankNumber:        dto.BankNumber,
		BankAccountNumber: dto.BankAccountNumber,
		BranchCode:        dto.BranchCode,
		AccountOwner:      dto.AccountOwner,
		Reference:         dto.Reference,
		BicSwift:          dto.BicSwift,
		CurrentAmount:     dto.CurrentAmount,
		Fees:              dto.Fees,
	}
	//for _, id := range dto.FeeIds {
	//	fee, err := as.feeRepo.GetFeeById(id)
	//	if err != nil {
	//		return nil, err
	//	}
	//	accountModel.Fees = append(accountModel.Fees, *fee)
	//}

	if dto.BankId != 0 {
		bank, err := as.bankRepo.GetOne(dto.BankId)
		if err != nil {
			return nil, err
		}
		accountModel.Bank = *bank
	}

	currency, err := as.currencyRepo.GetOne(dto.CurrencyId)
	if err != nil {
		return nil, err
	}
	accountModel.Currency = *currency
	if err = as.repo.Create(&accountModel); err != nil {
		return nil, err
	}
	return &accountModel, nil
}

func (as *AccountService) CreateMinimal(dto *dto.MinimalAccountCreateDto) (*models.Account, error) {
	var accountModel = models.Account{
		Name:    dto.Name,
		OwnerId: dto.OwnerId,
	}
	currency, err := as.currencyRepo.GetOne(dto.CurrencyId)
	if err != nil {
		return nil, err
	}
	accountModel.Currency = *currency
	if err = as.repo.Create(&accountModel); err != nil {
		return nil, err
	}
	return &accountModel, nil
}

func (as *AccountService) GetOwnBalances(userId uint, currencyId uint) (decimal.Decimal, error) {
	accounts, err := as.repo.GetOwnAccounts(userId, nil)
	if err != nil {
		return decimal.Zero, err
	}
	var balance = decimal.Zero
	for _, account := range accounts {
		if currencyId == account.Currency.ID {
			balance = balance.Add(account.CurrentAmount)
		} else {
			currencyExchange, err := as.currencyRepo.GetCurrencyExchangeBidirectional(account.Currency.ID, currencyId)
			if err != nil {
				return decimal.Zero, err
			}

			if currencyExchange.ID == 0 {
				return decimal.Zero, fmt.Errorf("could not find exchange rate between currencies with ids %d and %d", account.Currency.ID, currencyId)
			}
			var convertedAmount decimal.Decimal
			if currencyExchange.FirstCurrencyId == account.Currency.ID {
				convertedAmount = account.CurrentAmount.Mul(currencyExchange.FirstCurrencySell)
			} else {
				convertedAmount = account.CurrentAmount.Mul(currencyExchange.SecondCurrencySell)
			}
			balance = balance.Add(convertedAmount)
		}
	}
	return balance, nil
}

func (as *AccountService) GetOwnAccounts(ownerId uint, query *dto.AccountOwnQuery) ([]models.Account, error) {
	return as.repo.GetOwnAccounts(ownerId, query)
}

func (as *AccountService) GetAllAccounts(query *dto.AccountQuery, pag *dto.PaginationDto) (*dto.GetAllAccountDto, error) {
	return as.repo.GetAllAccounts(query, pag)
}

func (as *AccountService) GetAccountById(id uint) (*models.Account, error) {
	return as.repo.GetAccountById(id)
}

func (as *AccountService) UpdateAccount(account *models.Account) error {
	return as.repo.UpdateAccount(account)
}

func (as *AccountService) UpdateAmount(id uint, newAmount decimal.Decimal) error {
	return as.repo.UpdateAmount(id, newAmount)
}

func (as *AccountService) DeleteAccountById(Id uint) error {
	return as.repo.DeleteAccount(Id)
}

func (as *AccountService) CheckCurrencyLimit(currencyId uint) (bool, error) {
	var query = dto.AccountQuery{
		CurrencyId: currencyId,
	}
	var pag dto.PaginationDto
	allAcounts, err := as.repo.GetAllAccounts(&query, &pag)
	if err != nil {
		return false, err
	}
	return allAcounts.Count <= 5, nil
}
