package controllers

import (
	"errors"
	"net/http"
	"transaction-service/internal/constants"
	"transaction-service/internal/dto"
	"transaction-service/internal/middleware"
	"transaction-service/internal/models"
	"transaction-service/internal/services"
	"transaction-service/utils/parsers"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AccountController struct {
	service     services.AccountServiceInterface
	userService services.UserServiceInterface
}

func NewAccountController(service services.AccountServiceInterface, userService services.UserServiceInterface) *AccountController {
	return &AccountController{
		service:     service,
		userService: userService,
	}
}

func (ac *AccountController) Register(r *gin.RouterGroup, s string) *gin.RouterGroup {
	g := r.Group(s)
	g.POST("create", middleware.Authentication(ac.userService, constants.OwnerRole), AppHandler(ac.CreateAccount).Handle)
	g.POST("create-my-account", middleware.Authentication(ac.userService, constants.OwnerRole), App<PERSON>andler(ac.CreateMinimalAccount).Handle)
	g.GET("get-own-accounts", middleware.Authentication(ac.userService, constants.OwnerRole), AppHandler(ac.GetOwnAccounts).Handle)
	g.GET("get-own-balances/:currency", middleware.Authentication(ac.userService, constants.OwnerRole), AppHandler(ac.GetOwnBalances).Handle)
	g.GET("get-all", middleware.Authentication(ac.userService, constants.AdminRole), AppHandler(ac.GetAllAccounts).Handle)
	g.GET("get-one/:id", middleware.Authentication(ac.userService, constants.AdminRole), AppHandler(ac.GetOneAccountById).Handle)
	g.PATCH("update/:id", middleware.Authentication(ac.userService, constants.SuperAdminRole), AppHandler(ac.UpdateAccount).Handle)
	g.DELETE("/:account_id", middleware.Authentication(ac.userService, constants.AdminRole), AppHandler(ac.DeleteAccount).Handle)
	return g
}

func (ac *AccountController) GetOneAccountById(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")
	res, err := ac.service.GetAccountById(parsers.ParamUint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, res)
}

func (ac *AccountController) UpdateAccount(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")
	var updateDto models.Account
	if err := ctx.ShouldBindJSON(&updateDto); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	account, err := ac.service.GetAccountById(parsers.ParamUint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	updateDto.OwnerId = account.OwnerId
	updateDto.ID = account.ID
	updateDto.CreatedAt = account.CreatedAt
	if err = ac.service.UpdateAccount(&updateDto); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, account)
}

func (ac *AccountController) GetAllAccounts(ctx *gin.Context) *AppResp {
	var queryInput dto.AccountQuery
	if err := ctx.ShouldBindQuery(&queryInput); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}

	var pag dto.PaginationDto
	if err := ctx.ShouldBindQuery(&pag); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}

	pag.Default()
	resp, err := ac.service.GetAllAccounts(&queryInput, &pag)
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, resp)
}

func (ac *AccountController) GetOwnAccounts(ctx *gin.Context) *AppResp {
	userId, _ := ctx.Get("user_id")
	var queryInput dto.AccountOwnQuery
	if err := ctx.ShouldBindQuery(&queryInput); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}

	accounts, err := ac.service.GetOwnAccounts(userId.(uint), &queryInput)
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, accounts)
}

func (ac *AccountController) CreateAccount(ctx *gin.Context) *AppResp {
	userId, _ := ctx.Get("user_id")
	role, _ := ctx.Get("role")

	var input dto.AccountCreateDto
	if err := ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}

	if role == constants.SuperAdminRole {
		if input.OwnerId == 0 {
			return &AppResp{
				Error:   "owner id is required",
				Message: "bad request",
				Code:    http.StatusBadRequest,
			}
		}
	} else {
		input.OwnerId = userId.(uint)
	}
	allowedLimit, err := ac.service.CheckCurrencyLimit(input.CurrencyId)
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	if !allowedLimit {
		return &AppResp{
			Message: "Max accounts limit reached. You can create up to 5 accounts per currency.",
			Code:    http.StatusBadRequest,
		}
	}
	account, err := ac.service.Create(&input)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}

	return Ok(ctx, account)
}

func (ac *AccountController) CreateMinimalAccount(ctx *gin.Context) *AppResp {
	userId, _ := ctx.Get("user_id")
	role, _ := ctx.Get("role")
	var input dto.MinimalAccountCreateDto
	if err := ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	if role == constants.SuperAdminRole {
		if input.OwnerId == 0 {
			return &AppResp{
				Error:   "owner id is required",
				Message: "bad request",
				Code:    http.StatusBadRequest,
			}
		}
	} else {
		input.OwnerId = userId.(uint)
	}
	allowedLimit, err := ac.service.CheckCurrencyLimit(input.CurrencyId)
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	if !allowedLimit {
		return &AppResp{
			Message: "Max accounts limit reached. You can create up to 5 accounts per currency.",
			Code:    http.StatusBadRequest,
		}
	}

	account, err := ac.service.CreateMinimal(&input)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}

	return Ok(ctx, account)
}

func (ac *AccountController) GetOwnBalances(ctx *gin.Context) *AppResp {
	userId, _ := ctx.Get("user_id")
	currency := ctx.Param("currency")
	currencyId := parsers.ParamUint(currency)
	balances, err := ac.service.GetOwnBalances(userId.(uint), currencyId)
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, balances)
}

func (ac *AccountController) DeleteAccount(ctx *gin.Context) *AppResp {
	accountId := ctx.Param("account_id")
	account, err := ac.service.GetAccountById(parsers.ParamUint(accountId))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	if !account.CurrentAmount.IsZero() {
		return &AppResp{
			Error:   "account has non null balance",
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	err = ac.service.DeleteAccountById(parsers.ParamUint(accountId))
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "account deleted")
}
