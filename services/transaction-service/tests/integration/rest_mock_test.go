package integration

import (
	"errors"
	"os"
	"testing"
	"transaction-service/core/connect"
	"transaction-service/core/server"
	"transaction-service/internal/integration/rest"
	"transaction-service/tests/mocks"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestFetchUserRoles_MockedResponses demonstrates how to use the mock client
func TestFetchUserRoles_MockedResponses(t *testing.T) {
	// Skip if TEST_ENV not set
	if os.Getenv("TEST_ENV") != "true" {
		t.Skip("Skipping integration test: TEST_ENV not set")
	}

	// Initialize test server
	s := &server.Server{Test: true}
	_, _, err := s.InitTest()
	require.NoError(t, err)

	// Get the mock client from connect
	restMock, ok := connect.TestRestMock.(*mocks.RestClientMock)
	require.True(t, ok, "TestRestMock should be a RestClientMock")

	t.Run("successful response", func(t *testing.T) {
		// Reset mock for clean test
		restMock.Reset()

		// Setup mock response
		testAdmins := []rest.AdminsDto{
			{UserID: 1, Role: "super_admin"},
			{UserID: 2, Role: "admin"},
		}
		restMock.MockFetchUserRolesSuccess(testAdmins)

		// Call the function
		admins, err := rest.FetchUserRoles()

		// Assertions
		assert.NoError(t, err)
		assert.Len(t, admins, 2)
		assert.Equal(t, uint(1), admins[0].UserID)
		assert.Equal(t, "super_admin", admins[0].Role)
		assert.Equal(t, uint(2), admins[1].UserID)
		assert.Equal(t, "admin", admins[1].Role)

		// Verify the call was made
		assert.True(t, restMock.VerifyFetchUserRolesCalled())
		assert.Equal(t, 1, restMock.GetFetchUserRolesCallCount())
	})

	t.Run("unauthorized error", func(t *testing.T) {
		// Reset mock for clean test
		restMock.Reset()

		// Setup mock error response
		restMock.MockFetchUserRolesUnauthorized()

		// Call the function
		admins, err := rest.FetchUserRoles()

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, admins)
		assert.Contains(t, err.Error(), "unexpected status code: 401")

		// Verify the call was made
		assert.True(t, restMock.VerifyFetchUserRolesCalled())
	})

	t.Run("internal server error", func(t *testing.T) {
		// Reset mock for clean test
		restMock.Reset()

		// Setup mock error response
		restMock.MockFetchUserRolesInternalError()

		// Call the function
		admins, err := rest.FetchUserRoles()

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, admins)
		assert.Contains(t, err.Error(), "unexpected status code: 500")
	})

	t.Run("network error", func(t *testing.T) {
		// Reset mock for clean test
		restMock.Reset()

		// Setup mock network error
		networkErr := errors.New("connection refused")
		restMock.MockFetchUserRolesNetworkError(networkErr)

		// Call the function
		admins, err := rest.FetchUserRoles()

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, admins)
		assert.Contains(t, err.Error(), "request failed")
		assert.Contains(t, err.Error(), "connection refused")
	})

	t.Run("timeout error", func(t *testing.T) {
		// Reset mock for clean test
		restMock.Reset()

		// Setup mock timeout
		restMock.MockFetchUserRolesTimeout()

		// Call the function
		admins, err := rest.FetchUserRoles()

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, admins)
		assert.Contains(t, err.Error(), "timeout")
	})

	t.Run("empty response", func(t *testing.T) {
		// Reset mock for clean test
		restMock.Reset()

		// Setup empty but successful response
		restMock.MockFetchUserRolesSuccess([]rest.AdminsDto{})

		// Call the function
		admins, err := rest.FetchUserRoles()

		// Assertions
		assert.NoError(t, err)
		assert.Len(t, admins, 0)
	})

	t.Run("custom response", func(t *testing.T) {
		// Reset mock for clean test
		restMock.Reset()

		// Setup custom mock response using the underlying mock client
		mockClient := restMock.GetMockClient()
		mockClient.SetJSONResponse("GET", "http://localhost:8095api/v1/user/get-all-admins", 
			`[{"UserId":999,"Role":"custom_role"}]`)

		// Call the function
		admins, err := rest.FetchUserRoles()

		// Assertions
		assert.NoError(t, err)
		assert.Len(t, admins, 1)
		assert.Equal(t, uint(999), admins[0].UserID)
		assert.Equal(t, "custom_role", admins[0].Role)
	})
}

// TestFetchUserRoles_AuthorizationHeader tests that the correct authorization header is sent
func TestFetchUserRoles_AuthorizationHeader(t *testing.T) {
	// Skip if TEST_ENV not set
	if os.Getenv("TEST_ENV") != "true" {
		t.Skip("Skipping integration test: TEST_ENV not set")
	}

	// Initialize test server
	s := &server.Server{Test: true}
	_, _, err := s.InitTest()
	require.NoError(t, err)

	// Get the mock client from connect
	restMock, ok := connect.TestRestMock.(*mocks.RestClientMock)
	require.True(t, ok, "TestRestMock should be a RestClientMock")

	// Reset mock for clean test
	restMock.Reset()

	// Setup mock response
	restMock.MockFetchUserRolesSuccess(mocks.CreateTestAdmins())

	// Call the function
	_, err = rest.FetchUserRoles()
	require.NoError(t, err)

	// Verify authorization header was sent correctly
	// Note: This assumes the test config has a specific token
	// You might need to adjust this based on your test configuration
	requests := restMock.GetMockClient().GetRequestsForURL("api/v1/user/get-all-admins")
	require.Len(t, requests, 1)

	authHeader, exists := requests[0].Headers["Authorization"]
	assert.True(t, exists, "Authorization header should be present")
	assert.Contains(t, authHeader, "Bearer", "Authorization header should contain Bearer token")
}

// TestFetchUserRoles_NoMockFallback demonstrates behavior when no mock is set
func TestFetchUserRoles_NoMockFallback(t *testing.T) {
	// Skip if TEST_ENV not set
	if os.Getenv("TEST_ENV") != "true" {
		t.Skip("Skipping integration test: TEST_ENV not set")
	}

	// Initialize test server
	s := &server.Server{Test: true}
	_, _, err := s.InitTest()
	require.NoError(t, err)

	// Get the mock client from connect
	restMock, ok := connect.TestRestMock.(*mocks.RestClientMock)
	require.True(t, ok, "TestRestMock should be a RestClientMock")

	// Reset mock and don't set any responses
	restMock.Reset()

	// Call the function - should get default 404 response
	admins, err := rest.FetchUserRoles()

	// Assertions - should get an error due to 404 status
	assert.Error(t, err)
	assert.Nil(t, admins)
	assert.Contains(t, err.Error(), "unexpected status code: 404")
}
