package examples

import (
	"errors"
	"os"
	"testing"
	"transaction-service/core/connect"
	"transaction-service/core/server"
	"transaction-service/internal/integration/rest"
	core_test "transaction-service/tests/core"
	"transaction-service/tests/mocks"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Example test showing how to use the mock system in integration tests
func TestMockUsageExample(t *testing.T) {
	// Skip if TEST_ENV not set (following existing pattern)
	if os.Getenv("TEST_ENV") != "true" {
		t.Skip("Skipping integration test: TEST_ENV not set")
	}

	// Initialize test server (this automatically sets up mocks)
	s := &server.Server{Test: true}
	_, _, err := s.InitTest()
	require.NoError(t, err)

	// Get the mock client from connect
	restMock, ok := connect.TestRestMock.(*mocks.RestClientMock)
	require.True(t, ok, "TestRestMock should be a RestClientMock")

	t.Run("example_successful_scenario", func(t *testing.T) {
		// Reset mock for clean test
		restMock.Reset()

		// Setup mock response for successful case
		testAdmins := []rest.AdminsDto{
			{UserID: 1, Role: "super_admin"},
			{UserID: 2, Role: "admin"},
			{UserID: 3, Role: "user"},
		}
		restMock.MockFetchUserRolesSuccess(testAdmins)

		// Call the function that uses FetchUserRoles
		admins, err := rest.FetchUserRoles()

		// Verify the results
		assert.NoError(t, err)
		assert.Len(t, admins, 3)
		assert.Equal(t, uint(1), admins[0].UserID)
		assert.Equal(t, "super_admin", admins[0].Role)

		// Verify the call was made
		assert.True(t, restMock.VerifyFetchUserRolesCalled())
		assert.Equal(t, 1, restMock.GetFetchUserRolesCallCount())
	})

	t.Run("example_error_handling", func(t *testing.T) {
		// Reset mock for clean test
		restMock.Reset()

		// Setup mock error response
		restMock.MockFetchUserRolesUnauthorized()

		// Call the function
		admins, err := rest.FetchUserRoles()

		// Verify error handling
		assert.Error(t, err)
		assert.Nil(t, admins)
		assert.Contains(t, err.Error(), "unexpected status code: 401")

		// Verify the call was made
		assert.True(t, restMock.VerifyFetchUserRolesCalled())
	})

	t.Run("example_network_error", func(t *testing.T) {
		// Reset mock for clean test
		restMock.Reset()

		// Setup mock network error
		networkErr := errors.New("connection timeout")
		restMock.MockFetchUserRolesNetworkError(networkErr)

		// Call the function
		admins, err := rest.FetchUserRoles()

		// Verify error handling
		assert.Error(t, err)
		assert.Nil(t, admins)
		assert.Contains(t, err.Error(), "request failed")
		assert.Contains(t, err.Error(), "connection timeout")
	})

	t.Run("example_custom_response", func(t *testing.T) {
		// Reset mock for clean test
		restMock.Reset()

		// Setup custom response using the underlying mock client
		mockClient := restMock.GetMockClient()

		// Note: You need to use the full URL that matches your config
		// This is just an example - adjust the URL based on your test config
		mockClient.SetJSONResponse("GET", "http://localhost:8095api/v1/user/get-all-admins",
			`[{"UserId":999,"Role":"custom_test_role"}]`)

		// Call the function
		admins, err := rest.FetchUserRoles()

		// Verify custom response
		assert.NoError(t, err)
		assert.Len(t, admins, 1)
		assert.Equal(t, uint(999), admins[0].UserID)
		assert.Equal(t, "custom_test_role", admins[0].Role)
	})

	t.Run("example_multiple_calls", func(t *testing.T) {
		// Reset mock for clean test
		restMock.Reset()

		// Setup mock response
		restMock.MockFetchUserRolesSuccess(mocks.CreateTestAdmins())

		// Make multiple calls
		_, err1 := rest.FetchUserRoles()
		_, err2 := rest.FetchUserRoles()
		_, err3 := rest.FetchUserRoles()

		// Verify all calls succeeded
		assert.NoError(t, err1)
		assert.NoError(t, err2)
		assert.NoError(t, err3)

		// Verify call count
		assert.Equal(t, 3, restMock.GetFetchUserRolesCallCount())
	})

	t.Run("example_no_mock_fallback", func(t *testing.T) {
		// Reset mock and don't set any responses
		restMock.Reset()

		// Call the function - should get default 404 response
		admins, err := rest.FetchUserRoles()

		// Verify fallback behavior
		assert.Error(t, err)
		assert.Nil(t, admins)
		assert.Contains(t, err.Error(), "unexpected status code: 404")
	})
}

// Example showing how to test a function that calls FetchUserRoles
func TestFunctionThatUsesFetchUserRoles(t *testing.T) {
	// Skip if TEST_ENV not set
	if os.Getenv("TEST_ENV") != "true" {
		t.Skip("Skipping integration test: TEST_ENV not set")
	}

	// Initialize test server
	s := &server.Server{Test: true}
	_, _, err := s.InitTest()
	require.NoError(t, err)

	// Get the mock client
	restMock, ok := connect.TestRestMock.(*core_test.RestClientMock)
	require.True(t, ok)

	t.Run("test_business_logic_with_mocked_external_call", func(t *testing.T) {
		// Reset mock
		restMock.Reset()

		// Setup mock response for the external service call
		testAdmins := mocks.CreateSingleAdmin(123, "admin")
		restMock.MockFetchUserRolesSuccess(testAdmins)

		// Call your business logic function that internally uses FetchUserRoles
		result := someBusinessLogicFunction()

		// Verify the business logic worked correctly
		assert.True(t, result)

		// Verify the external service was called
		assert.True(t, restMock.VerifyFetchUserRolesCalled())
	})

	t.Run("test_error_handling_in_business_logic", func(t *testing.T) {
		// Reset mock
		restMock.Reset()

		// Setup mock error to test error handling
		restMock.MockFetchUserRolesInternalError()

		// Call your business logic function
		result := someBusinessLogicFunction()

		// Verify error was handled correctly
		assert.False(t, result)

		// Verify the external service was called
		assert.True(t, restMock.VerifyFetchUserRolesCalled())
	})
}

// Example business logic function that uses FetchUserRoles
func someBusinessLogicFunction() bool {
	// This is just an example function that calls FetchUserRoles
	admins, err := rest.FetchUserRoles()
	if err != nil {
		// Handle error
		return false
	}

	// Some business logic
	return len(admins) > 0
}

// Example showing how to verify specific request details
func TestVerifyRequestDetails(t *testing.T) {
	// Skip if TEST_ENV not set
	if os.Getenv("TEST_ENV") != "true" {
		t.Skip("Skipping integration test: TEST_ENV not set")
	}

	// Initialize test server
	s := &server.Server{Test: true}
	_, _, err := s.InitTest()
	require.NoError(t, err)

	// Get the mock client
	restMock, ok := connect.TestRestMock.(*mocks.RestClientMock)
	require.True(t, ok)

	t.Run("verify_authorization_header", func(t *testing.T) {
		// Reset mock
		restMock.Reset()

		// Setup mock response
		restMock.MockFetchUserRolesSuccess(mocks.CreateTestAdmins())

		// Call the function
		_, err := rest.FetchUserRoles()
		require.NoError(t, err)

		// Get the underlying mock client to inspect requests
		mockClient := restMock.GetMockClient()
		requests := mockClient.GetRequestsForURL("api/v1/user/get-all-admins")

		require.Len(t, requests, 1)

		// Verify request details
		req := requests[0]
		assert.Equal(t, "GET", req.Method)
		assert.Contains(t, req.URL, "api/v1/user/get-all-admins")

		// Verify authorization header is present
		authHeader, exists := req.Headers["Authorization"]
		assert.True(t, exists)
		assert.Contains(t, authHeader, "Bearer")
	})
}
