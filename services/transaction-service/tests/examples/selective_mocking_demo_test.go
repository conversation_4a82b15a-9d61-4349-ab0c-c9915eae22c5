package examples

import (
	"os"
	"testing"
	"transaction-service/core/connect"
	"transaction-service/core/server"
	httpClient "transaction-service/core/http"
	"transaction-service/internal/integration/rest"
	"transaction-service/tests/mocks"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestSelectiveMockingDemo demonstrates the new selective mocking capabilities
func TestSelectiveMockingDemo(t *testing.T) {
	// Skip if TEST_ENV not set
	if os.Getenv("TEST_ENV") != "true" {
		t.Skip("Skipping integration test: TEST_ENV not set")
	}

	// Initialize test server (automatically sets up centralized HTTP client)
	s := &server.Server{Test: true}
	_, _, err := s.InitTest()
	require.NoError(t, err)

	t.Run("selective_mocking_specific_requests", func(t *testing.T) {
		// Get the mock client from connect
		restMock, ok := connect.TestRestMock.(*mocks.RestClientMock)
		require.True(t, ok, "TestRestMock should be a RestClientMock")

		// Reset for clean test
		restMock.Reset()

		// Setup mock for specific request
		testAdmins := []rest.AdminsDto{
			{UserID: 1, Role: "super_admin"},
			{UserID: 2, Role: "admin"},
		}
		restMock.MockFetchUserRolesSuccess(testAdmins)

		// This call will use the mock
		admins, err := rest.FetchUserRoles()
		assert.NoError(t, err)
		assert.Len(t, admins, 2)
		assert.Equal(t, "super_admin", admins[0].Role)

		// Verify the mock was used
		assert.True(t, restMock.VerifyFetchUserRolesCalled())
		assert.Equal(t, 1, restMock.GetFetchUserRolesCallCount())
	})

	t.Run("disable_mocking_uses_real_client", func(t *testing.T) {
		// Disable mocking to demonstrate fallback to real client
		httpClient.DisableMock()

		// This would make a real HTTP request (will likely fail in test environment)
		// but demonstrates that the real client is being used
		_, err := rest.FetchUserRoles()
		
		// We expect an error because we're not mocking and there's no real auth service
		assert.Error(t, err)
		
		// Re-enable mocking for other tests
		httpClient.EnableMock()
	})

	t.Run("mock_different_scenarios", func(t *testing.T) {
		// Get the mock client
		restMock, ok := connect.TestRestMock.(*mocks.RestClientMock)
		require.True(t, ok)

		// Test unauthorized scenario
		restMock.Reset()
		restMock.MockFetchUserRolesUnauthorized()

		admins, err := rest.FetchUserRoles()
		assert.Error(t, err)
		assert.Nil(t, admins)
		assert.Contains(t, err.Error(), "401")

		// Test timeout scenario
		restMock.Reset()
		restMock.MockFetchUserRolesTimeout()

		admins, err = rest.FetchUserRoles()
		assert.Error(t, err)
		assert.Nil(t, admins)
		assert.Contains(t, err.Error(), "timeout")

		// Test successful scenario again
		restMock.Reset()
		restMock.MockFetchUserRolesSuccess(mocks.CreateSingleAdmin(999, "test_role"))

		admins, err = rest.FetchUserRoles()
		assert.NoError(t, err)
		assert.Len(t, admins, 1)
		assert.Equal(t, uint(999), admins[0].UserID)
		assert.Equal(t, "test_role", admins[0].Role)
	})

	t.Run("direct_http_client_access", func(t *testing.T) {
		// Demonstrate direct access to the centralized HTTP client
		assert.NotNil(t, connect.HTTPClient)

		// Check if it's a selective client
		if selectiveClient, ok := connect.HTTPClient.(*httpClient.SelectiveHTTPClient); ok {
			assert.True(t, selectiveClient.IsMockEnabled())
			
			// Can disable/enable mocking directly
			selectiveClient.DisableMock()
			assert.False(t, selectiveClient.IsMockEnabled())
			
			selectiveClient.EnableMock()
			assert.True(t, selectiveClient.IsMockEnabled())
		}
	})

	t.Run("custom_mock_responses", func(t *testing.T) {
		// Get the underlying mock client for custom responses
		restMock, ok := connect.TestRestMock.(*mocks.RestClientMock)
		require.True(t, ok)

		restMock.Reset()

		// Setup custom response using the underlying mock client
		mockClient := restMock.GetMockClient()
		
		// Mock a custom response (adjust URL based on your test config)
		mockClient.SetJSONResponse("GET", "api/v1/user/get-all-admins", 
			`[{"UserId":777,"Role":"custom_test_role"}]`)

		// Call the function
		admins, err := rest.FetchUserRoles()
		assert.NoError(t, err)
		assert.Len(t, admins, 1)
		assert.Equal(t, uint(777), admins[0].UserID)
		assert.Equal(t, "custom_test_role", admins[0].Role)

		// Verify request details
		requests := mockClient.GetRequestsForURL("api/v1/user/get-all-admins")
		assert.Len(t, requests, 1)
		assert.Equal(t, "GET", requests[0].Method)
		
		// Check authorization header
		authHeader, exists := requests[0].Headers["Authorization"]
		assert.True(t, exists)
		assert.Contains(t, authHeader, "Bearer")
	})
}

// TestArchitecturalBenefits demonstrates the benefits of the new architecture
func TestArchitecturalBenefits(t *testing.T) {
	// Skip if TEST_ENV not set
	if os.Getenv("TEST_ENV") != "true" {
		t.Skip("Skipping integration test: TEST_ENV not set")
	}

	// Initialize test server
	s := &server.Server{Test: true}
	_, _, err := s.InitTest()
	require.NoError(t, err)

	t.Run("centralized_management", func(t *testing.T) {
		// HTTP client is centrally managed
		assert.NotNil(t, connect.HTTPClient, "HTTP client should be centrally available")
		
		// All services can access the same client
		// This ensures consistency across the application
	})

	t.Run("selective_mocking_flexibility", func(t *testing.T) {
		// Can selectively enable/disable mocking
		httpClient.DisableMock()
		assert.False(t, httpClient.IsMockEnabled())
		
		httpClient.EnableMock()
		assert.True(t, httpClient.IsMockEnabled())
		
		// This allows tests to:
		// 1. Mock specific requests while allowing others to use real client
		// 2. Test error handling with mocked failures
		// 3. Test integration scenarios with real services when needed
	})

	t.Run("clean_separation_of_concerns", func(t *testing.T) {
		// HTTP client management is separated from business logic
		// Mock setup is separated from test logic
		// Configuration is centralized
		
		// This makes the code:
		// - More maintainable
		// - Easier to test
		// - More flexible for different environments
		
		assert.True(t, true, "Architecture provides clean separation of concerns")
	})

	t.Run("backward_compatibility", func(t *testing.T) {
		// FetchUserRoles function signature unchanged
		admins, err := rest.FetchUserRoles()
		
		// Function still works the same way from the outside
		// Internal implementation is improved but external API is preserved
		_ = admins
		_ = err
		
		assert.True(t, true, "External API remains unchanged")
	})
}

// TestErrorHandlingScenarios demonstrates comprehensive error handling
func TestErrorHandlingScenarios(t *testing.T) {
	// Skip if TEST_ENV not set
	if os.Getenv("TEST_ENV") != "true" {
		t.Skip("Skipping integration test: TEST_ENV not set")
	}

	// Initialize test server
	s := &server.Server{Test: true}
	_, _, err := s.InitTest()
	require.NoError(t, err)

	restMock, ok := connect.TestRestMock.(*mocks.RestClientMock)
	require.True(t, ok)

	scenarios := []struct {
		name     string
		setup    func()
		errorMsg string
	}{
		{
			name: "network_timeout",
			setup: func() {
				restMock.Reset()
				restMock.MockFetchUserRolesTimeout()
			},
			errorMsg: "timeout",
		},
		{
			name: "unauthorized_access",
			setup: func() {
				restMock.Reset()
				restMock.MockFetchUserRolesUnauthorized()
			},
			errorMsg: "401",
		},
		{
			name: "internal_server_error",
			setup: func() {
				restMock.Reset()
				restMock.MockFetchUserRolesInternalError()
			},
			errorMsg: "500",
		},
		{
			name: "malformed_response",
			setup: func() {
				restMock.Reset()
				mockClient := restMock.GetMockClient()
				mockClient.SetSuccessResponse("GET", "api/v1/user/get-all-admins", "invalid json")
			},
			errorMsg: "parse",
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.name, func(t *testing.T) {
			scenario.setup()

			admins, err := rest.FetchUserRoles()

			assert.Error(t, err)
			assert.Nil(t, admins)
			assert.Contains(t, err.Error(), scenario.errorMsg)
		})
	}
}
