package tests_api

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"testing"
	"time"
	"transaction-service/core/connect"
	"transaction-service/core/server"

	"go.uber.org/zap"
)

func TestMain(m *testing.M) {
	ctx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()

	// Initialize test server with test flag
	s := server.Server{Test: true}

	// Wait for dependencies to be ready
	time.Sleep(10 * time.Second)

	_, rabHandler, e := s.InitTest()
	if e != nil {
		fmt.Printf("Error initializing test server: %s\n", e.Error())
		return
	}

	// Run worker in a goroutine
	go func() {
		if err := rabHandler.Start(ctx); err != nil {
			connect.Logger.Fatal("Worker encountered an error", zap.Error(err))
		}
	}()

	var sources []string
	// flag.Var(&sources, "s", "Specify the source for seeding")
	// flag.Parse()
	// Seed test data
	server.Runner(sources)
	fmt.Println("Test server starting on port 8081...")
	code := m.Run()

	os.Exit(code)
	<-ctx.Done()
	stop()
	log.Println("Shutting down test server gracefully, press Ctrl+C again to force")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := s.Srv.Shutdown(ctx); err != nil {
		log.Fatal("Test server forced to shutdown: ", err)
	}

	log.Println("Test server exiting")
}
