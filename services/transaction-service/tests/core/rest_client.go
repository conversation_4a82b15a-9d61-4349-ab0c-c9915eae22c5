package core_test

import (
	httpClient "transaction-service/core/http"
)

// RestClientMock provides convenient methods for mocking REST service calls
type RestClientMock struct {
	mockClient *MockHTTPClient
}

// NewRestClientMock creates a new REST client mock
func NewRestClientMock() *RestClientMock {
	return &RestClientMock{
		mockClient: NewMockHTTPClient(),
	}
}

// Setup configures the mock client in the centralized HTTP client
func (r *RestClientMock) Setup() {
	httpClient.SetMockClient(r.mockClient)
}

// Reset clears all mock responses and captured requests
func (r *RestClientMock) Reset() {
	r.mockClient.Reset()
}

// GetMockClient returns the underlying mock HTTP client
func (r *RestClientMock) GetMockClient() *MockHTTPClient {
	return r.mockClient
}
