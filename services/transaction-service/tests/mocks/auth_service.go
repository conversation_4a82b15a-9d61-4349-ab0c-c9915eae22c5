package mocks

import (
	"fmt"
	"net/http"
	"transaction-service/core/config"
	"transaction-service/internal/integration/rest"
)



// MockFetchUserRolesSuccess mocks a successful FetchUserRoles response
func (r *RestClientMock) MockFetchUserRolesSuccess(admins []rest.AdminsDto) {
	url := r.buildAuthServiceURL("api/v1/user/get-all-admins")

	// Convert admins to JSON string
	jsonBody := r.adminsToJSON(admins)

	r.mockClient.SetJSONResponse("GET", url, jsonBody)
}

// MockFetchUserRolesError mocks an error response for FetchUserRoles
func (r *RestClientMock) MockFetchUserRolesError(statusCode int, errorMessage string) {
	url := r.buildAuthServiceURL("api/v1/user/get-all-admins")
	r.mockClient.SetErrorResponse("GET", url, statusCode, errorMessage)
}

// MockFetchUserRolesNetworkError mocks a network error for FetchUserRoles
func (r *RestClientMock) MockFetchUserRolesNetworkError(err error) {
	url := r.buildAuthServiceURL("api/v1/user/get-all-admins")
	r.mockClient.SetError("GET", url, err)
}

// MockFetchUserRolesUnauthorized mocks an unauthorized response
func (r *RestClientMock) MockFetchUserRolesUnauthorized() {
	r.MockFetchUserRolesError(http.StatusUnauthorized, "Unauthorized")
}

// MockFetchUserRolesInternalError mocks an internal server error
func (r *RestClientMock) MockFetchUserRolesInternalError() {
	r.MockFetchUserRolesError(http.StatusInternalServerError, "Internal Server Error")
}

// MockFetchUserRolesTimeout mocks a timeout error
func (r *RestClientMock) MockFetchUserRolesTimeout() {
	r.MockFetchUserRolesNetworkError(fmt.Errorf("request timeout"))
}

// Verification methods

// VerifyFetchUserRolesCalled verifies that FetchUserRoles was called
func (r *RestClientMock) VerifyFetchUserRolesCalled() bool {
	url := r.buildAuthServiceURL("api/v1/user/get-all-admins")
	requests := r.mockClient.GetRequestsForURL("api/v1/user/get-all-admins")

	for _, req := range requests {
		if req.Method == "GET" && req.URL == url {
			return true
		}
	}
	return false
}

// VerifyFetchUserRolesCalledWithAuth verifies that FetchUserRoles was called with proper authorization
func (r *RestClientMock) VerifyFetchUserRolesCalledWithAuth(expectedToken string) bool {
	requests := r.mockClient.GetRequestsForURL("api/v1/user/get-all-admins")

	for _, req := range requests {
		if req.Method == "GET" {
			if authHeader, exists := req.Headers["Authorization"]; exists {
				expectedAuth := "Bearer " + expectedToken
				if authHeader == expectedAuth {
					return true
				}
			}
		}
	}
	return false
}

// GetFetchUserRolesCallCount returns the number of times FetchUserRoles was called
func (r *RestClientMock) GetFetchUserRolesCallCount() int {
	requests := r.mockClient.GetRequestsForURL("api/v1/user/get-all-admins")
	count := 0
	for _, req := range requests {
		if req.Method == "GET" {
			count++
		}
	}
	return count
}

// Helper methods

// buildAuthServiceURL builds the full URL for auth service endpoints
func (r *RestClientMock) buildAuthServiceURL(endpoint string) string {
	// Get the auth service host from config
	// Note: In tests, this should be the test configuration
	return config.Get().AuthService.Host + endpoint
}

// adminsToJSON converts AdminsDto slice to JSON string
func (r *RestClientMock) adminsToJSON(admins []rest.AdminsDto) string {
	if len(admins) == 0 {
		return "[]"
	}

	jsonStr := "["
	for i, admin := range admins {
		if i > 0 {
			jsonStr += ","
		}
		jsonStr += fmt.Sprintf(`{"UserId":%d,"Role":"%s"}`, admin.UserID, admin.Role)
	}
	jsonStr += "]"

	return jsonStr
}

// Predefined mock data for common scenarios

// CreateTestAdmins creates test admin data
func CreateTestAdmins() []rest.AdminsDto {
	return []rest.AdminsDto{
		{UserID: 1, Role: "super_admin"},
		{UserID: 2, Role: "admin"},
		{UserID: 3, Role: "user"},
	}
}

// CreateSingleAdmin creates a single admin for testing
func CreateSingleAdmin(userID uint, role string) []rest.AdminsDto {
	return []rest.AdminsDto{
		{UserID: userID, Role: role},
	}
}

// Common mock scenarios

// SetupDefaultMocks sets up common successful responses
func (r *RestClientMock) SetupDefaultMocks() {
	// Mock successful FetchUserRoles with default test data
	r.MockFetchUserRolesSuccess(CreateTestAdmins())
}

// SetupErrorMocks sets up common error scenarios
func (r *RestClientMock) SetupErrorMocks() {
	// This can be used to test error handling paths
	r.MockFetchUserRolesInternalError()
}

// SetupEmptyResponse sets up an empty but successful response
func (r *RestClientMock) SetupEmptyResponse() {
	r.MockFetchUserRolesSuccess([]rest.AdminsDto{})
}

