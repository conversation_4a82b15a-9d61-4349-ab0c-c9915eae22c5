# HTTP Client Mocking for Transaction Service

This package provides a flexible HTTP client mocking system for testing the transaction service, specifically designed to mock external service calls like `FetchUserRoles`.

## Overview

The mocking system consists of two main components:

1. **MockHTTPClient** - A generic HTTP client mock that can simulate any HTTP request/response
2. **RestClientMock** - A higher-level wrapper that provides convenient methods for mocking REST service calls

## Quick Start

### Basic Usage in Tests

```go
func TestSomething(t *testing.T) {
    // Skip if TEST_ENV not set
    if os.Getenv("TEST_ENV") != "true" {
        t.Skip("Skipping integration test: TEST_ENV not set")
    }

    // Initialize test server (this automatically sets up mocks)
    s := &server.Server{Test: true}
    _, _, err := s.InitTest()
    require.NoError(t, err)

    // Get the mock client
    restMock, ok := connect.TestRestMock.(*mocks.RestClientMock)
    require.True(t, ok)

    // Reset for clean test
    restMock.Reset()

    // Setup your mock responses
    restMock.MockFetchUserRolesSuccess([]rest.AdminsDto{
        {UserID: 1, Role: "admin"},
    })

    // Call your code that uses FetchUserRoles
    result, err := someFunction()

    // Verify results and calls
    assert.NoError(t, err)
    assert.True(t, restMock.VerifyFetchUserRolesCalled())
}
```

## MockHTTPClient

The low-level HTTP client mock that implements the `HTTPClient` interface.

### Setting Up Responses

```go
client := mocks.NewMockHTTPClient()

// Basic response
client.SetResponse("GET", "http://example.com/api", 200, "success")

// Response with headers
client.SetResponseWithHeaders("GET", "http://example.com/api", 200, "success", map[string]string{
    "Content-Type": "application/json",
})

// Error response
client.SetError("GET", "http://example.com/api", errors.New("network error"))

// Helper methods
client.SetSuccessResponse("GET", "http://example.com/api", "ok")
client.SetErrorResponse("GET", "http://example.com/api", 500, "server error")
client.SetJSONResponse("GET", "http://example.com/api", `{"key":"value"}`)
```

### Verifying Requests

```go
// Get all captured requests
requests := client.GetRequests()

// Get requests for specific URL pattern
userRequests := client.GetRequestsForURL("api/users")

// Get request count
count := client.GetRequestCount()

// Clear captured requests
client.ClearRequests()
```

## RestClientMock

Higher-level wrapper for common REST service mocking scenarios.

### Auth Service Mocking

```go
restMock := mocks.NewRestClientMock()
restMock.Setup() // This sets the mock client in the rest package

// Successful responses
testAdmins := []rest.AdminsDto{{UserID: 1, Role: "admin"}}
restMock.MockFetchUserRolesSuccess(testAdmins)

// Error responses
restMock.MockFetchUserRolesUnauthorized()
restMock.MockFetchUserRolesInternalError()
restMock.MockFetchUserRolesTimeout()

// Network errors
restMock.MockFetchUserRolesNetworkError(errors.New("connection refused"))

// Custom responses
restMock.MockFetchUserRolesError(418, "I'm a teapot")
```

### Verification Methods

```go
// Verify calls were made
assert.True(t, restMock.VerifyFetchUserRolesCalled())

// Verify authorization header
assert.True(t, restMock.VerifyFetchUserRolesCalledWithAuth("expected-token"))

// Get call count
count := restMock.GetFetchUserRolesCallCount()
```

### Predefined Test Data

```go
// Create test admin data
admins := mocks.CreateTestAdmins() // Returns 3 test admins

// Create single admin
admin := mocks.CreateSingleAdmin(123, "custom_role")
```

## Test Environment Setup

The mock system is automatically configured when using the test server:

```go
// In core/server/tests.go, the InitTest function automatically:
// 1. Creates a RestClientMock
// 2. Sets it up in the rest package
// 3. Configures default mock responses
// 4. Stores it in connect.TestRestMock for test access
```

## Advanced Usage

### Custom Mock Responses

```go
// Access the underlying mock client for full control
mockClient := restMock.GetMockClient()
mockClient.SetJSONResponse("GET", "http://custom.com/api", `{"custom":"response"}`)
```

### Multiple Service Mocking

```go
// The system is designed to be extensible for other services
// You can create similar mock methods for other external services
```

### Fallback Behavior

If no mock response is configured for a request:
- The mock client returns a 404 "Mock response not found" response
- This allows tests to fail gracefully when mocks are not properly set up
- Real requests are never made in the test environment

## Best Practices

1. **Always reset mocks between tests**:
   ```go
   restMock.Reset()
   ```

2. **Use specific mock methods when available**:
   ```go
   // Preferred
   restMock.MockFetchUserRolesSuccess(admins)
   
   // Instead of
   mockClient.SetJSONResponse("GET", "...", "...")
   ```

3. **Verify calls were made**:
   ```go
   assert.True(t, restMock.VerifyFetchUserRolesCalled())
   ```

4. **Test error scenarios**:
   ```go
   restMock.MockFetchUserRolesUnauthorized()
   restMock.MockFetchUserRolesTimeout()
   ```

5. **Use the TEST_ENV check**:
   ```go
   if os.Getenv("TEST_ENV") != "true" {
       t.Skip("Skipping integration test: TEST_ENV not set")
   }
   ```

## Error Handling Testing

The mock system supports testing various error scenarios:

- **HTTP Error Codes**: 401, 403, 500, etc.
- **Network Errors**: Connection refused, timeout, DNS errors
- **Malformed Responses**: Invalid JSON, unexpected format
- **Empty Responses**: Empty arrays, null responses

## Thread Safety

The mock client is thread-safe and can handle concurrent requests in tests.

## Extending the System

To add mocking for new external services:

1. Add new methods to `RestClientMock`
2. Follow the naming pattern: `Mock[ServiceName][MethodName][Scenario]`
3. Add corresponding verification methods
4. Update the default mocks setup if needed

Example:
```go
func (r *RestClientMock) MockNotificationServiceSendSuccess() {
    // Implementation
}

func (r *RestClientMock) VerifyNotificationServiceSendCalled() bool {
    // Implementation
}
```
