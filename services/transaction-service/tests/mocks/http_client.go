package mocks

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
)

// MockHTTPClient is a mock implementation of HTTPClient interface
type MockHTTPClient struct {
	responses map[string]*MockResponse
	requests  []MockRequest
	mutex     sync.RWMutex
}

// MockResponse represents a mock HTTP response
type MockResponse struct {
	StatusCode int
	Body       string
	Headers    map[string]string
	Error      error
}

// MockRequest represents a captured HTTP request
type MockRequest struct {
	Method  string
	URL     string
	Headers map[string]string
	Body    string
}

// NewMockHTTPClient creates a new mock HTTP client
func NewMockHTTPClient() *MockHTTPClient {
	return &MockHTTPClient{
		responses: make(map[string]*MockResponse),
		requests:  make([]MockRequest, 0),
	}
}

// Do implements the HTTPClient interface
func (m *MockHTTPClient) Do(req *http.Request) (*http.Response, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Capture the request
	body := ""
	if req.Body != nil {
		bodyBytes, _ := io.ReadAll(req.Body)
		body = string(bodyBytes)
		// Reset the body for potential reuse
		req.Body = io.NopCloser(bytes.NewReader(bodyBytes))
	}

	headers := make(map[string]string)
	for key, values := range req.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	mockReq := MockRequest{
		Method:  req.Method,
		URL:     req.URL.String(),
		Headers: headers,
		Body:    body,
	}
	m.requests = append(m.requests, mockReq)

	// Find matching mock response
	key := m.buildKey(req.Method, req.URL.String())
	if mockResp, exists := m.responses[key]; exists {
		if mockResp.Error != nil {
			return nil, mockResp.Error
		}

		// Create HTTP response
		resp := &http.Response{
			StatusCode: mockResp.StatusCode,
			Body:       io.NopCloser(strings.NewReader(mockResp.Body)),
			Header:     make(http.Header),
		}

		// Set headers
		for key, value := range mockResp.Headers {
			resp.Header.Set(key, value)
		}

		return resp, nil
	}

	// If no mock response found, return a default 404
	return &http.Response{
		StatusCode: http.StatusNotFound,
		Body:       io.NopCloser(strings.NewReader("Mock response not found")),
		Header:     make(http.Header),
	}, nil
}

// SetResponse sets a mock response for a specific method and URL
func (m *MockHTTPClient) SetResponse(method, url string, statusCode int, body string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	key := m.buildKey(method, url)
	m.responses[key] = &MockResponse{
		StatusCode: statusCode,
		Body:       body,
		Headers:    make(map[string]string),
	}
}

// SetResponseWithHeaders sets a mock response with custom headers
func (m *MockHTTPClient) SetResponseWithHeaders(method, url string, statusCode int, body string, headers map[string]string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	key := m.buildKey(method, url)
	m.responses[key] = &MockResponse{
		StatusCode: statusCode,
		Body:       body,
		Headers:    headers,
	}
}

// SetError sets a mock error for a specific method and URL
func (m *MockHTTPClient) SetError(method, url string, err error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	key := m.buildKey(method, url)
	m.responses[key] = &MockResponse{
		Error: err,
	}
}

// SetResponseForURLPattern sets a response for URLs containing a pattern
func (m *MockHTTPClient) SetResponseForURLPattern(method, urlPattern string, statusCode int, body string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	key := m.buildKey(method, urlPattern)
	m.responses[key] = &MockResponse{
		StatusCode: statusCode,
		Body:       body,
		Headers:    make(map[string]string),
	}
}

// GetRequests returns all captured requests
func (m *MockHTTPClient) GetRequests() []MockRequest {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// Return a copy to prevent race conditions
	result := make([]MockRequest, len(m.requests))
	copy(result, m.requests)
	return result
}

// GetRequestCount returns the number of requests made
func (m *MockHTTPClient) GetRequestCount() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return len(m.requests)
}

// GetRequestsForURL returns requests made to a specific URL pattern
func (m *MockHTTPClient) GetRequestsForURL(urlPattern string) []MockRequest {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var result []MockRequest
	for _, req := range m.requests {
		if strings.Contains(req.URL, urlPattern) {
			result = append(result, req)
		}
	}
	return result
}

// ClearRequests clears all captured requests
func (m *MockHTTPClient) ClearRequests() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.requests = m.requests[:0]
}

// ClearResponses clears all mock responses
func (m *MockHTTPClient) ClearResponses() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.responses = make(map[string]*MockResponse)
}

// Reset clears both requests and responses
func (m *MockHTTPClient) Reset() {
	m.ClearRequests()
	m.ClearResponses()
}

// buildKey creates a key for storing responses
func (m *MockHTTPClient) buildKey(method, url string) string {
	return fmt.Sprintf("%s:%s", method, url)
}

// Helper methods for common scenarios

// SetSuccessResponse sets a 200 OK response
func (m *MockHTTPClient) SetSuccessResponse(method, url, body string) {
	m.SetResponse(method, url, http.StatusOK, body)
}

// SetErrorResponse sets an error status response
func (m *MockHTTPClient) SetErrorResponse(method, url string, statusCode int, errorMessage string) {
	m.SetResponse(method, url, statusCode, errorMessage)
}

// SetJSONResponse sets a JSON response with appropriate content type
func (m *MockHTTPClient) SetJSONResponse(method, url, jsonBody string) {
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	m.SetResponseWithHeaders(method, url, http.StatusOK, jsonBody, headers)
}
